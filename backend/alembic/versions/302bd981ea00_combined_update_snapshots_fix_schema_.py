"""combined_update_snapshots_fix_schema_convert_identity

Revision ID: 302bd981ea00
Revises: 75c7333d90e6
Create Date: 2025-07-15 11:17:39.410807

"""

from typing import Sequence, Union

from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision: str = "302bd981ea00"
down_revision: Union[str, None] = "75c7333d90e6"
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    # 1. Update snapshots table - remove old columns and add new project-specific columns
    op.drop_column("snapshots", "current_cash")
    op.drop_column("snapshots", "anticipated_cash")

    # Add new project-specific columns to snapshots table
    op.add_column(
        "snapshots", sa.Column("current_projects_expenses", sa.String(), nullable=True)
    )
    op.add_column(
        "snapshots",
        sa.Column("current_projects_purchase_orders", sa.String(), nullable=True),
    )
    op.add_column(
        "snapshots", sa.Column("current_projects_invoices", sa.String(), nullable=True)
    )
    op.add_column(
        "snapshots",
        sa.Column("anticipated_projects_expenses", sa.String(), nullable=True),
    )
    op.add_column(
        "snapshots",
        sa.Column("anticipated_projects_purchase_orders", sa.String(), nullable=True),
    )
    op.add_column(
        "snapshots",
        sa.Column("anticipated_projects_invoices", sa.String(), nullable=True),
    )

    # 2. Fix tag_mappings column types from BIGINT to Integer
    op.alter_column(
        "tag_mappings",
        "tag_id",
        existing_type=sa.BIGINT(),
        type_=sa.Integer(),
        existing_nullable=False,
    )

    op.alter_column(
        "tag_mappings",
        "entity_id",
        existing_type=sa.BIGINT(),
        type_=sa.Integer(),
        existing_nullable=False,
    )

    # Add missing unique constraint on tag_mappings (only if it doesn't exist)
    connection = op.get_bind()
    result = connection.execute(
        sa.text(
            "SELECT 1 FROM pg_constraint WHERE conname = 'uix_tag_mapping' AND conrelid = 'tag_mappings'::regclass"
        )
    )
    constraint_exists = result.scalar()
    
    if not constraint_exists:
        op.create_unique_constraint(
            "uix_tag_mapping", "tag_mappings", ["tag_id", "entity_type", "entity_id"]
        )

    # Remove the unique index on users.email (if it exists)
    try:
        op.drop_index("ix_users_email", table_name="users")
    except Exception:
        # Index might not exist, ignore the error
        pass

    # 3. Convert tags.id from sequence-based to identity column
    # First, get the current sequence value to preserve continuity
    connection = op.get_bind()
    result = connection.execute(sa.text("SELECT last_value FROM tags_id_seq"))
    current_value = result.scalar() or 0

    # Remove the default (sequence) from the column
    op.alter_column(
        "tags",
        "id",
        existing_type=sa.BIGINT(),
        server_default=None,
        existing_nullable=False,
    )

    # Drop the sequence
    op.execute("DROP SEQUENCE IF EXISTS tags_id_seq CASCADE")

    # Add identity to the column, starting from the current sequence value
    op.execute(
        f"ALTER TABLE tags ALTER COLUMN id ADD GENERATED BY DEFAULT AS IDENTITY (START WITH {current_value + 1})"
    )


def downgrade() -> None:
    # Reverse all changes in reverse order

    # 1. Convert back from identity to sequence for tags.id
    # First, get the current identity value
    connection = op.get_bind()
    result = connection.execute(sa.text("SELECT COALESCE(MAX(id), 0) FROM tags"))
    current_value = result.scalar() or 0

    # Remove identity from the column
    op.execute("ALTER TABLE tags ALTER COLUMN id DROP IDENTITY IF EXISTS")

    # Create the sequence
    op.execute(f"CREATE SEQUENCE tags_id_seq START WITH {current_value + 1}")

    # Set the sequence as the default for the column
    op.alter_column(
        "tags",
        "id",
        existing_type=sa.BIGINT(),
        server_default=sa.text("nextval('tags_id_seq'::regclass)"),
        existing_nullable=False,
    )

    # Set the sequence owner to the column (for proper CASCADE behavior)
    op.execute("ALTER SEQUENCE tags_id_seq OWNED BY tags.id")

    # 2. Reverse schema fixes
    # Add back the unique index on users.email
    op.create_index("ix_users_email", "users", ["email"], unique=True)

    # Remove unique constraint from tag_mappings
    op.drop_constraint("uix_tag_mapping", "tag_mappings", type_="unique")

    # Revert tag_mappings column types back to BIGINT
    op.alter_column(
        "tag_mappings",
        "entity_id",
        existing_type=sa.Integer(),
        type_=sa.BIGINT(),
        existing_nullable=False,
    )

    op.alter_column(
        "tag_mappings",
        "tag_id",
        existing_type=sa.Integer(),
        type_=sa.BIGINT(),
        existing_nullable=False,
    )

    # 3. Reverse snapshots table changes
    # Remove new project-specific columns from snapshots table
    op.drop_column("snapshots", "anticipated_projects_invoices")
    op.drop_column("snapshots", "anticipated_projects_purchase_orders")
    op.drop_column("snapshots", "anticipated_projects_expenses")
    op.drop_column("snapshots", "current_projects_invoices")
    op.drop_column("snapshots", "current_projects_purchase_orders")
    op.drop_column("snapshots", "current_projects_expenses")

    # Add back old columns to snapshots table
    op.add_column(
        "snapshots", sa.Column("anticipated_cash", sa.String(), nullable=True)
    )
    op.add_column("snapshots", sa.Column("current_cash", sa.String(), nullable=True))
