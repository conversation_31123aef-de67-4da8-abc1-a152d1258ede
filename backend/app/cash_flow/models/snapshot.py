from datetime import datetime
from typing import Optional, List
from sqlmodel import SQLModel, Field, BigInteger
from pydantic import validator
from sqlalchemy import text, String
import json

from .utils import parse_date_util


class SnapshotBase(SQLModel):
    name: str
    start_date: datetime
    end_date: datetime
    timestamp: Optional[datetime] = Field(
        default=None, sa_column_kwargs={"server_default": text("NOW()")}
    )
    week_dates: Optional[str] = Field(default=None, sa_type=String)
    working_capital: Optional[str] = Field(default=None, sa_type=String)
    savings_balance: Optional[str] = Field(default=None, sa_type=String)
    current_projects_expenses: Optional[str] = Field(default=None, sa_type=String)
    current_projects_purchase_orders: Optional[str] = Field(
        default=None, sa_type=String
    )
    current_projects_invoices: Optional[str] = Field(default=None, sa_type=String)
    anticipated_projects_expenses: Optional[str] = Field(default=None, sa_type=String)
    anticipated_projects_purchase_orders: Optional[str] = Field(
        default=None, sa_type=String
    )
    anticipated_projects_invoices: Optional[str] = Field(default=None, sa_type=String)
    current_projects_working_capital: Optional[str] = Field(
        default=None, sa_type=String
    )
    anticipated_projects_working_capital: Optional[str] = Field(
        default=None, sa_type=String
    )
    current_projects_savings: Optional[str] = Field(default=None, sa_type=String)
    anticipated_projects_savings: Optional[str] = Field(default=None, sa_type=String)

    @validator("start_date", "end_date", pre=True)
    def parse_date(cls, v):
        return parse_date_util(v)

    @validator(
        "week_dates",
        "working_capital",
        "savings_balance",
        "current_projects_expenses",
        "current_projects_purchase_orders",
        "current_projects_invoices",
        "anticipated_projects_expenses",
        "anticipated_projects_purchase_orders",
        "anticipated_projects_invoices",
        "current_projects_working_capital",
        "anticipated_projects_working_capital",
        "current_projects_savings",
        "anticipated_projects_savings",
        pre=True,
    )
    def parse_json_string(cls, v):
        if isinstance(v, str):
            try:
                return json.loads(v)
            except json.JSONDecodeError:
                return None
        return v


class SnapshotTable(SnapshotBase, table=True):
    __tablename__ = "snapshots"
    id: Optional[int] = Field(sa_type=BigInteger, default=None, primary_key=True)
    organization_id: int = Field(sa_type=BigInteger, foreign_key="organizations.id")


class SnapshotUpsert(SnapshotBase):
    id: Optional[int] = None
    organization_id: Optional[int] = None


class SnapshotResponse(SnapshotBase):
    id: int
    organization_id: int
    week_dates: Optional[List[str]] = None
    working_capital: Optional[List[float]] = None
    savings_balance: Optional[List[float]] = None
    current_projects_expenses: Optional[List[float]] = None
    current_projects_purchase_orders: Optional[List[float]] = None
    current_projects_invoices: Optional[List[float]] = None
    anticipated_projects_expenses: Optional[List[float]] = None
    anticipated_projects_purchase_orders: Optional[List[float]] = None
    anticipated_projects_invoices: Optional[List[float]] = None
    current_projects_working_capital: Optional[List[float]] = None
    anticipated_projects_working_capital: Optional[List[float]] = None
    current_projects_savings: Optional[List[float]] = None
    anticipated_projects_savings: Optional[List[float]] = None

    @property
    def id(self) -> int:
        return self.id
