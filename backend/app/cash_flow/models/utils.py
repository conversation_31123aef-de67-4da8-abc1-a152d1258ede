from datetime import datetime, date


def parse_amount(value):
    """Parse amount from various formats to float."""
    if value is None:
        return 0.0
    if isinstance(value, (int, float)):
        return float(value)
    if isinstance(value, str):
        # Remove currency symbols, commas, and other non-numeric characters except decimal point
        cleaned = value.replace("$", "").replace(",", "").replace(" ", "")
        try:
            return float(cleaned)
        except ValueError:
            return 0.0
    return 0.0


def parse_date_util(value):
    """Parse date from various formats to datetime."""
    if value is None:
        return None
    if isinstance(value, datetime):
        # If the datetime is timezone-aware, convert to naive datetime
        if value.tzinfo is not None:
            return value.replace(tzinfo=None)
        return value
    if isinstance(value, str):
        try:
            # Parse ISO format and convert to naive datetime
            parsed = datetime.fromisoformat(value.replace("Z", "+00:00"))
            if parsed.tzinfo is not None:
                return parsed.replace(tzinfo=None)
            return parsed
        except ValueError:
            try:
                return datetime.strptime(value, "%Y-%m-%d")
            except ValueError:
                try:
                    # Parse MM/dd/yyyy format (e.g., "04/24/2025")
                    return datetime.strptime(value, "%m/%d/%Y")
                except ValueError:
                    return None
    return None


def to_date_only(dt: datetime) -> date:
    """Convert datetime to date-only (no time component)."""
    if dt is None:
        return dt
    if dt.tzinfo is not None:
        dt = dt.replace(tzinfo=None)
    return dt.date()


def merge_models(model_1, model_2):
    """Merge two models, using values from model_1 where not None, otherwise from model_2."""
    for field in model_1.model_fields:
        if getattr(model_1, field) is not None:
            setattr(model_2, field, getattr(model_1, field))
    return model_2
